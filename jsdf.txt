{
  "security": {
    "encryption_key": "L2FlGQC3NXk1BEzYdqbuT-d8hVrzsAXKvhiy-2k3ddc=",
    "key_rotation_days": 7,
    "sanitize_inputs": true,
    "request_signatures": true,
    "audit_logging": true,
    "max_retries": 3,
    "max_file_size": 10485760
  },
  "database": {
    "dsn": "sqlite+aiosqlite:///./socialsearch_audit.db"
  },
  "platforms": {
    "facebook": { "enabled": false },
    "linkedin": { "enabled": false },
    "instagram": {
      "enabled": true,
      "api_type": "rapidapi",
      "api_endpoint": "https://rocketapi-for-developers.p.rapidapi.com/instagram/search",
      "rapidapi_host": "rocketapi-for-developers.p.rapidapi.com",
      "rapidapi_key": "**************************************************"
    },
    "snapchat": { "enabled": false },
    "tiktok": { "enabled": false }
  },
  "image_analysis": {
    "enable_facial_recognition": true,
    "face_detection_model": "hog",
    "min_confidence_threshold": 0.6  // <-- FIX THIS: Make sure it's a number, not null
  },
  "search": {
    "default_name": "cristano",
    "photo_path": nullC:\\Users\\<USER>\\Documents\\MY PROJECTS\\Social search API\\profile4.jpg,
    "platforms_to_search": ["instagram"],
    "min_relevance_score": 40.0
  },
  "cache": { // <-- ADD THIS ENTIRE SECTION
    "enabled": true,
    "ttl_seconds": 3600
  }
}