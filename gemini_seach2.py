
import asyncio
import aiofiles
import base64
import hashlib
import json
import secrets
import io
import os
from datetime import datetime, timed<PERSON><PERSON>
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

import httpx
import magic # python-magic
import structlog
from cryptography.fernet import Fernet # cryptography
from pydantic import BaseModel, Field, SecretStr, field_validator, ConfigDict
from tenacity import AsyncRetrying, stop_after_attempt, wait_exponential, retry_if_exception

try:
    import face_recognition # face_recognition
    from PIL import Image # Pillow
    import numpy as np # numpy, dependency of face_recognition but good to make explicit for our use
    FACE_RECOGNITION_AVAILABLE = True
except ImportError:
    FACE_RECOGNITION_AVAILABLE = False
    face_recognition = None # type: ignore
    Image = None # type: ignore
    np = None # type: ignore

# Structured Logging Configuration
structlog.configure(
    processors=[
        structlog.contextvars.merge_contextvars,
        structlog.processors.add_log_level,
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.JSONRenderer(),
    ],
    wrapper_class=structlog.BoundLogger,
    logger_factory=structlog.PrintLoggerFactory(), # For production, consider structlog.stdlib.LoggerFactory()
)
logger = structlog.get_logger(__name__)

if not FACE_RECOGNITION_AVAILABLE:
    logger.warning("`face_recognition`, `Pillow`, or `numpy` library not found. Facial recognition features will be disabled.")

# Platform Enumeration
class PlatformType(str, Enum):
    FACEBOOK = "facebook"
    LINKEDIN = "linkedin"
    INSTAGRAM = "instagram"
    SNAPCHAT = "snapchat"
    TIKTOK = "tiktok"

# Security Configuration
class SecurityConfig(BaseModel):
    model_config = ConfigDict(validate_assignment=True)

    encryption_key: SecretStr = Field(..., min_length=43) # Fernet keys are typically 44 chars (base64 of 32 bytes)
    key_rotation_days: int = Field(7, ge=1)
    sanitize_inputs: bool = True # Placeholder for actual input sanitization logic if needed beyond audit
    request_signatures: bool = True
    audit_logging: bool = True
    max_retries: int = Field(3, ge=1, le=10)
    max_file_size: int = Field(10_485_760, ge=1_048_576)  # 10MB
    network_timeout_seconds: float = Field(30.0, ge=5.0)
    network_max_connections: int = Field(100, ge=10)
    network_rate_limit_permits: int = Field(50, ge=5)
    circuit_breaker_failure_threshold: int = Field(10, ge=3)
    circuit_breaker_open_delay_seconds: int = Field(60, ge=10)
    circuit_breaker_half_open_delay_seconds: int = Field(120, ge=30)

    @field_validator("encryption_key")
    @classmethod
    def validate_key_is_valid_fernet_key(cls, v: SecretStr) -> SecretStr:
        try:
            key_bytes_b64 = v.get_secret_value().encode('ascii') # Fernet keys are ASCII (base64)
            decoded_key = base64.urlsafe_b64decode(key_bytes_b64)
            if len(decoded_key) != 32:
                raise ValueError("Encryption key, when decoded, must be 32 bytes long.")
            Fernet(key_bytes_b64) # Attempt to initialize Fernet to fully validate key format
        except (TypeError, ValueError, base64.binascii.Error) as e:
            raise ValueError(f"Encryption key must be a valid URL-safe base64-encoded string representing 32 raw bytes: {e}")
        return v

    def rotate_key(self) -> None:
        new_fernet_key_bytes = Fernet.generate_key() # This is already bytes, base64 encoded
        self.encryption_key = SecretStr(new_fernet_key_bytes.decode('ascii'))
        logger.info("Security key rotated successfully", new_key_type=type(self.encryption_key))


# Image Analysis Configuration
class ImageAnalysisConfig(BaseModel):
    model_config = ConfigDict(validate_assignment=True)
    enable_facial_recognition: bool = Field(True)
    face_detection_model: str = Field("hog", pattern="^(hog|cnn)$")
    min_confidence_threshold: Optional[float] = Field(0.6, ge=0.0, le=1.0) # Tolerance for face_recognition.compare_faces


# Network Manager helper for tenacity retry predicate
def _should_retry_httpx_exception(exception: BaseException) -> bool:
    if isinstance(exception, (httpx.NetworkError, httpx.TimeoutException, httpx.ConnectError)):
        logger.debug("Retrying due to network/timeout/connect error", error_type=type(exception).__name__, error_msg=str(exception))
        return True
    if isinstance(exception, httpx.HTTPStatusError):
        is_retriable_status = exception.response.status_code >= 500 or exception.response.status_code == 429 # Server errors or Too Many Requests
        log_func = logger.debug if is_retriable_status else logger.info
        log_func(f"{'Retrying' if is_retriable_status else 'Not retrying'} due to HTTP status error",
                 status_code=exception.response.status_code, url=str(exception.request.url))
        return is_retriable_status
    logger.debug("Not retrying for this exception type in tenacity predicate", error_type=type(exception).__name__, error_msg=str(exception))
    return False

# Network Manager
class NetworkManager:
    def __init__(self, security: SecurityConfig):
        self.security = security
        self.client = httpx.AsyncClient(
            limits=httpx.Limits(max_connections=self.security.network_max_connections),
            timeout=httpx.Timeout(self.security.network_timeout_seconds)
        )
        self.circuit_state = "closed" # "closed", "open", "half-open"
        self.rate_limiter = asyncio.Semaphore(self.security.network_rate_limit_permits)
        self.request_metrics = {"total": 0, "success": 0, "failures": 0, "latency_seconds": []}

    def _sign_request(self, headers: Dict[str, str]) -> Dict[str, str]:
        if self.security.request_signatures:
            timestamp = datetime.now().isoformat()
            # For robust security, HMAC-SHA256 with the key is better.
            # This is a simplified example.
            key_fragment = self.security.encryption_key.get_secret_value()[:16]
            payload_to_sign = f"{timestamp}{key_fragment}" # Consider request method and URL path too
            signature = hashlib.sha256(payload_to_sign.encode()).hexdigest()
            headers.update({"X-Request-Signature": signature, "X-Timestamp": timestamp})
        return headers

    def _record_success(self, start_time: datetime) -> None:
        self.request_metrics["success"] += 1
        self.request_metrics["latency_seconds"].append((datetime.now() - start_time).total_seconds())
        if self.circuit_state == "half-open":
            self.circuit_state = "closed"
            logger.info("Circuit breaker closed after successful request in half-open state.")
        elif self.circuit_state == "closed" and self.request_metrics["failures"] > 0:
             self.request_metrics["failures"] = max(0, self.request_metrics["failures"] - 1) # Decay failures on success

    def _record_failure(self, start_time: datetime) -> None:
        self.request_metrics["failures"] += 1
        self.request_metrics["latency_seconds"].append((datetime.now() - start_time).total_seconds())

        if self.circuit_state == "closed" and self.request_metrics["failures"] >= self.security.circuit_breaker_failure_threshold:
            self.circuit_state = "open"
            logger.warning(f"Circuit breaker opened due to {self.request_metrics['failures']} failures (threshold: {self.security.circuit_breaker_failure_threshold}).")
            asyncio.create_task(self._try_reclose_circuit(delay=self.security.circuit_breaker_open_delay_seconds))
        elif self.circuit_state == "half-open":
            self.circuit_state = "open"
            logger.warning("Circuit breaker re-opened after failure in half-open state.")
            asyncio.create_task(self._try_reclose_circuit(delay=self.security.circuit_breaker_half_open_delay_seconds))

    async def _try_reclose_circuit(self, delay: int):
        await asyncio.sleep(delay)
        if self.circuit_state == "open": # Ensure state hasn't changed
            self.circuit_state = "half-open"
            self.request_metrics["failures"] = 0 # Reset failures for half-open test period
            logger.info("Circuit breaker transitioned to half-open state. Will allow a test request.")

    async def secure_request(self, method: str, url: str, **kwargs) -> Optional[httpx.Response]:
        log_ctx = logger.bind(method=method, url=url)
        if self.circuit_state == "open":
            log_ctx.warning("Request blocked: Circuit breaker is open.")
            fake_request = httpx.Request(method, url) # For error context
            fake_response = httpx.Response(503, request=fake_request, text="Service Unavailable (Circuit Breaker Open)")
            raise httpx.HTTPStatusError("Circuit breaker open", request=fake_request, response=fake_response)

        self.request_metrics["total"] += 1
        request_start_time = datetime.now()

        async with self.rate_limiter:
            try:
                async for attempt in AsyncRetrying(
                    stop=stop_after_attempt(self.security.max_retries),
                    wait=wait_exponential(multiplier=1, min=1, max=10),
                    retry=retry_if_exception(_should_retry_httpx_exception),
                    reraise=True,
                    retry_error_callback=lambda rs: log_ctx.warning(
                        "Retrying request", attempt=rs.attempt_number, error=str(rs.outcome.exception()))
                ):
                    with attempt:
                        attempt_start_time = datetime.now()
                        current_headers = kwargs.pop("headers", {})
                        signed_headers = self._sign_request(current_headers)
                        
                        attempt_log_ctx = log_ctx.bind(attempt=attempt.retry_state.attempt_number)
                        attempt_log_ctx.debug("Making HTTP request")

                        response = await self.client.request(method, url, headers=signed_headers, **kwargs)
                        
                        attempt_log_ctx = attempt_log_ctx.bind(status_code=response.status_code)
                        response.raise_for_status() # Raises HTTPStatusError for 4xx/5xx

                        self._record_success(attempt_start_time)
                        attempt_log_ctx.info("Request attempt successful.")
                        return response
                # This part should ideally not be reached if reraise=True and an exception always occurs after retries
                log_ctx.error("All retry attempts failed, but no exception was reraised by Tenacity. This is unexpected.")
                return None # Fallback, though Tenacity with reraise=True should prevent this.

            except httpx.HTTPStatusError as http_err:
                self._record_failure(request_start_time)
                log_ctx.error(
                    "HTTP request failed after retries or due to non-retriable status",
                    status_code=http_err.response.status_code,
                    response_text=(http_err.response.text[:200] if http_err.response.text else "")
                )
                raise
            except Exception as exc: # Catches other errors (e.g., httpx.RequestError) not covered by retry predicate, or after retries for network errors
                self._record_failure(request_start_time)
                log_ctx.error(
                    "Network request failed due to an unexpected error after retries",
                    exc_info=True, exception_type=type(exc).__name__
                )
                return None # Adhere to Optional[httpx.Response] for non-HTTP errors after attempts
    
    async def close(self):
        await self.client.aclose()
        logger.info("NetworkManager client closed.")

# Platform Integrator Base Class
class PlatformIntegrator:
    def __init__(self, platform_name: PlatformType, config: Dict[str, Any], network: NetworkManager):
        self.platform_name = platform_name
        self.config = config # This is a dict derived from PlatformConfig.model_dump()
        self.network = network
        log_config_safe = {k: (str(v) if not isinstance(v, SecretStr) else "***REDACTED***")
                           for k, v in config.items()}
        logger.debug(f"Initializing {self.platform_name.value} integrator", config_summary=list(log_config_safe.keys()))


    async def search(self, name: str) -> Dict[str, Any]:
        raise NotImplementedError(f"Search method not implemented for {self.platform_name.value}")

# Platform Integrators
class FacebookIntegrator(PlatformIntegrator):
    def __init__(self, config: Dict[str, Any], network: NetworkManager):
        super().__init__(PlatformType.FACEBOOK, config, network)

    async def search(self, name: str) -> Dict[str, Any]:
        logger.warning("Facebook search requested, but no real API integration is implemented.", search_term=name, platform=self.platform_name.value)
        await asyncio.sleep(0.1) # Simulate API call
        return {"matches": [], "error": "Facebook API integration not implemented."}

class TikTokIntegrator(PlatformIntegrator):
    def __init__(self, config: Dict[str, Any], network: NetworkManager):
        super().__init__(PlatformType.TIKTOK, config, network)

    async def search(self, name: str) -> Dict[str, Any]:
        logger.warning("TikTok search requested, but no real API integration is implemented.", search_term=name, platform=self.platform_name.value)
        await asyncio.sleep(0.1)
        return {"matches": [], "error": "TikTok API integration not implemented."}

class InstagramIntegrator(PlatformIntegrator):
    def __init__(self, config: Dict[str, Any], network: NetworkManager):
        super().__init__(PlatformType.INSTAGRAM, config, network)

    async def search(self, name: str) -> Dict[str, Any]:
        log = logger.bind(search_term=name, platform=self.platform_name.value, api_type=self.config.get('api_type'))
        log.info("Instagram search initiated.")

        if self.config.get('api_type') != 'rapidapi':
            msg = f"Instagram search skipped: Configured api_type ('{self.config.get('api_type')}') is not 'rapidapi'."
            log.warning(msg)
            return {"matches": [], "error": msg}

        api_endpoint = self.config.get('api_endpoint')
        rapidapi_host = self.config.get('rapidapi_host')
        # self.config['rapidapi_key'] was SecretStr, model_dump makes it str if `mode='python'` wasn't used carefully
        # Assuming it's string here from model_dump(), or needs careful handling if SecretStr is passed as object.
        # For this structure, PlatformConfig.model_dump() creates a dict, SecretStr becomes plain string.
        # This implies SocialMapperPro._init_platforms should use model_dump(mode='json') or handle SecretStr manually if passed.
        # Let's assume it's already a string:
        rapidapi_key_value = self.config.get('rapidapi_key')


        if not api_endpoint or not rapidapi_host or not rapidapi_key_value:
            msg = "RapidAPI configuration (endpoint, host, or key) missing in Instagram config."
            log.error(msg)
            return {"matches": [], "error": msg}
        
        if rapidapi_key_value == "YOUR_RAPIDAPI_KEY_HERE":
            msg = "RapidAPI key is a placeholder. Please update the configuration."
            log.error(msg)
            return {"matches": [], "error": msg}

        headers = {
            'x-rapidapi-host': rapidapi_host,
            'x-rapidapi-key': rapidapi_key_value,
        }
        payload = {'query': name, 'count': self.config.get('default_count', 10)} # Add count from config
        log.debug("Sending POST request to Instagram RapidAPI.", url=api_endpoint, query_params=payload)

        try:
            response = await self.network.secure_request("POST", api_endpoint, headers=headers, json=payload)
            if response is None:
                log.error("Instagram API request via NetworkManager returned None.")
                return {"matches": [], "error": "API request failed: No response or unrecoverable network error."}

            user_data = response.json()
            log.debug("Instagram API response received successfully.", status_code=response.status_code, data_keys=list(user_data.keys()) if isinstance(user_data, dict) else None)

            matches = []
            users_list = user_data.get('response', {}).get('body', {}).get('users', [])
            if not users_list:
                log.info(f"No users found for query '{name}' in Instagram API response.")
                return {"matches": []} # No error, just no users

            for user_item in users_list:
                user_info = user_item.get('user', {})
                username = user_info.get('username')
                if not username:
                    log.debug("Skipping user item with no username.", item_data_keys=list(user_item.keys()))
                    continue

                full_name_lower = user_info.get('full_name', '').lower()
                search_name_lower = name.lower()
                if not (search_name_lower in full_name_lower or search_name_lower in username.lower()):
                    log.debug(f"Skipping user {username} due to name mismatch.", searched_name=search_name_lower, found_full_name=full_name_lower, found_username=username.lower())
                    continue
                
                is_verified = user_info.get('is_verified', False)
                follower_count = int(user_info.get('follower_count', 0) or 0)
                MIN_RELEVANCE_FOLLOWER_COUNT = self.config.get('extra_config', {}).get('min_relevance_followers', 1000)

                if not is_verified and follower_count < MIN_RELEVANCE_FOLLOWER_COUNT:
                    log.debug(f"Skipping low-relevance account: {username} (Not verified, {follower_count} followers. Threshold: {MIN_RELEVANCE_FOLLOWER_COUNT})")
                    continue

                match_data = {
                    "id": str(user_info.get('pk') or user_info.get('id') or username),
                    "name": user_info.get('full_name', name),
                    "username": username,
                    "platform": self.platform_name.value,
                    "profile_url": f"https://www.instagram.com/{username}",
                    "is_verified": is_verified,
                    "followers": follower_count,
                    "following": user_info.get('following_count'),
                    "posts": user_info.get('media_count'),
                    "bio": user_info.get('biography', ''),
                    "profile_pic_url": user_info.get('profile_pic_url')
                }
                matches.append({k: v for k, v in match_data.items() if v is not None})
            
            log.info(f"Found {len(matches)} relevant Instagram user(s) for query '{name}'.")
            return {"matches": matches}

        except httpx.HTTPStatusError as http_err:
            error_detail = f"Instagram API HTTP error: {http_err.response.status_code}"
            try:
                error_text = http_err.response.json()
                error_detail += f" - Detail: {json.dumps(error_text)}"
            except json.JSONDecodeError:
                error_detail += f" - Raw: {http_err.response.text[:200]}"
            log.error(error_detail, url=api_endpoint)
            return {"matches": [], "error": error_detail}
        except json.JSONDecodeError as json_err:
            raw_text = response.text[:200] if response else "N/A"
            log.error(f"Failed to parse Instagram API JSON response: {str(json_err)}", url=api_endpoint, raw_response_snippet=raw_text)
            return {"matches": [], "error": f"Invalid JSON response from Instagram API: {str(json_err)}"}
        except Exception as api_error:
            log.error(f"Unexpected error during Instagram API request or processing", exc_info=True, url=api_endpoint)
            return {"matches": [], "error": f"Instagram API request failed: {str(api_error)}"}


# Async Database (Simulated)
class AsyncDatabase:
    def __init__(self, dsn: str): # dsn is SecretStr.get_secret_value()
        self.dsn_type = dsn.split("://")[0] if "://" in dsn else "unknown_dsn_type"
        self.connected = False

    async def connect(self):
        try:
            await asyncio.sleep(0.05)
            self.connected = True
            logger.info("Database connection established (simulated)", dsn_type=self.dsn_type)
        except Exception as e:
            logger.error("Database connection failed (simulated)", dsn_type=self.dsn_type, error=str(e))
            self.connected = False
            raise

    async def disconnect(self):
        if self.connected:
            await asyncio.sleep(0.01)
            self.connected = False
            logger.info("Database connection closed (simulated)", dsn_type=self.dsn_type)

    async def log_audit(self, event: str, details: Dict[str, Any]):
        if not self.connected:
            logger.warning("Audit log skipped: Database not connected", audit_event_name=event)
            return

        log_entry = {"timestamp": datetime.now().isoformat(), "event_name": event, "details": details}
        try:
            await asyncio.sleep(0.005)
            logger.debug("Audit event logged to DB (simulated)", audit_event_name=event, detail_keys=list(details.keys()))
        except Exception as e:
            logger.error("Failed to write audit log to DB (simulated)", error=str(e), audit_event_name=event)


# Image Analysis Engine
class ImageAnalysisEngine:
    def __init__(self, security: SecurityConfig, analysis_config: ImageAnalysisConfig):
        self.security = security
        self.analysis_config = analysis_config
        # Each engine could have its own NetworkManager or share one. For simplicity, own instance.
        self.network = NetworkManager(security)
        try:
            self.fernet = Fernet(self.security.encryption_key.get_secret_value().encode('ascii'))
        except Exception as e:
            logger.critical("CRITICAL: Failed to initialize Fernet cipher for ImageAnalysisEngine.", exc_info=True)
            raise ValueError(f"Invalid encryption key for Fernet initialization: {e}")

        self.mime = magic.Magic(mime=True)
        self._check_dependencies()

    def _check_dependencies(self):
        if self.analysis_config.enable_facial_recognition and not FACE_RECOGNITION_AVAILABLE:
            logger.error("Facial recognition enabled in config, but dependencies (`face_recognition`, `Pillow`, `numpy`) are missing!")
            self.analysis_config.enable_facial_recognition = False # Auto-disable
            logger.warning("Facial recognition feature has been disabled due to missing dependencies.")

    async def _perform_face_recognition(self, image_bytes: bytes, model: str) -> Tuple[List[Tuple[int, ...]], List[np.ndarray]]:
        if not FACE_RECOGNITION_AVAILABLE or not self.analysis_config.enable_facial_recognition:
            return [], []
        
        log = logger.bind(face_detection_model=model)
        try:
            pil_image = await asyncio.to_thread(Image.open, io.BytesIO(image_bytes))
            if pil_image.mode != "RGB":
                pil_image = await asyncio.to_thread(pil_image.convert, "RGB")
            
            image_array = await asyncio.to_thread(np.array, pil_image)
            log.debug("Image loaded into numpy array for face recognition", shape=image_array.shape)

            face_locations = await asyncio.to_thread(face_recognition.face_locations, image_array, model=model)
            if not face_locations:
                log.debug("No faces detected in image.")
                return [], []
            log.info(f"Detected {len(face_locations)} face(s). Encoding them...")

            face_encodings_np = await asyncio.to_thread(face_recognition.face_encodings, image_array, known_face_locations=face_locations)
            log.debug(f"Generated {len(face_encodings_np)} face encoding(s).")
            return face_locations, face_encodings_np
        except Exception as e:
            log.error("Error during internal face recognition process", exc_info=True)
            raise RuntimeError(f"Face recognition internal error: {str(e)}")


    async def _process_single_profile_pic_match(
        self, input_image_encodings: List[np.ndarray], profile_info: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        username = profile_info.get("username")
        profile_pic_url = profile_info.get("profile_pic_url")
        log = logger.bind(profile_username=username, profile_pic_url=profile_pic_url)

        if not profile_pic_url: return None

        try:
            log.debug("Downloading profile picture for face comparison.")
            response = await self.network.secure_request("GET", profile_pic_url)
            if response is None or response.status_code != 200:
                log.warning(f"Failed to download profile picture. Status: {response.status_code if response else 'N/A'}")
                return None

            pic_content = response.content
            pic_type = await asyncio.to_thread(self.mime.from_buffer, pic_content)
            if not pic_type or not pic_type.startswith("image/"):
                log.warning(f"Invalid profile picture MIME type: {pic_type}")
                return None

            profile_face_locs, profile_face_encs = await self._perform_face_recognition(
                pic_content, self.analysis_config.face_detection_model)

            if not profile_face_encs:
                log.debug("No faces found in downloaded profile picture.")
                return None
            
            for i_enc_idx, input_enc in enumerate(input_image_encodings):
                comparisons = await asyncio.to_thread(
                    face_recognition.compare_faces, profile_face_encs, input_enc,
                    tolerance=self.analysis_config.min_confidence_threshold or 0.6)
                
                if any(comparisons):
                    distances = await asyncio.to_thread(face_recognition.face_distance, profile_face_encs, input_enc)
                    min_distance = float(distances.min()) if distances.size > 0 else float('inf')
                    log.info(f"Facial match FOUND for profile {username} with input face #{i_enc_idx}", distance=min_distance)
                    return {
                        "username": username, "platform": profile_info.get("platform"),
                        "profile_url": profile_info.get("profile_url"),
                        "match_confidence_distance": min_distance, # Lower is better
                        "profile_pic_url": profile_pic_url,
                        "input_face_index": i_enc_idx
                    }
            return None # No match
        except RuntimeError as face_rec_err:
            log.error(f"Face recognition failed for profile picture: {face_rec_err}")
        except httpx.HTTPStatusError as http_err:
            log.warning(f"HTTP error downloading profile pic for {username}: {http_err.response.status_code if http_err.response else 'N/A'}")
        except Exception:
            log.error(f"Unexpected error processing profile picture for {username}", exc_info=True)
        return None

    async def _compare_with_profile_pics(
        self, input_image_face_encodings: List[np.ndarray], profiles_with_pics: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        if not input_image_face_encodings or not profiles_with_pics: return []
        log = logger.bind(input_face_count=len(input_image_face_encodings), profile_pic_count=len(profiles_with_pics))
        log.info("Comparing input image faces against downloaded profile pictures.")

        tasks = [self._process_single_profile_pic_match(input_image_face_encodings, p_info) for p_info in profiles_with_pics]
        match_results = await asyncio.gather(*tasks)
        
        successful_matches = [match for match in match_results if match is not None]
        successful_matches.sort(key=lambda m: m["match_confidence_distance"])
        log.info(f"Found {len(successful_matches)} profile picture matches after comparison.")
        return successful_matches

    async def analyze(self, photo_path: Path, profile_pics_info: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
        analysis_result: Dict[str, Any] = {
            "status": "pending", "file_path": str(photo_path), "file_type": None, "size_bytes": None,
            "error_message": None, "facial_recognition_enabled": self.analysis_config.enable_facial_recognition,
            "face_count": 0, "face_locations": [], "face_encodings_count": 0, "profile_pic_matches": []
        }
        log = logger.bind(photo_path=str(photo_path))

        try:
            if not await asyncio.to_thread(photo_path.exists):
                raise FileNotFoundError(f"Image file not found: '{photo_path}'")

            stat_result = await asyncio.to_thread(photo_path.stat)
            analysis_result["size_bytes"] = stat_result.st_size
            if analysis_result["size_bytes"] > self.security.max_file_size:
                raise ValueError(f"File size ({analysis_result['size_bytes']}B) exceeds limit ({self.security.max_file_size}B).")

            async with aiofiles.open(photo_path, "rb") as f: image_content = await f.read()
            file_type = await asyncio.to_thread(self.mime.from_buffer, image_content)
            analysis_result["file_type"] = file_type
            if not file_type or not file_type.startswith("image/"):
                raise ValueError(f"Invalid file type: '{file_type}'. Expected an image.")
            log.info("Input image validated.", type=file_type, size_bytes=analysis_result["size_bytes"])

            if self.analysis_config.enable_facial_recognition and FACE_RECOGNITION_AVAILABLE:
                log.debug("Starting facial recognition on input image.")
                try:
                    face_locs, face_encs_np = await self._perform_face_recognition(image_content, self.analysis_config.face_detection_model)
                    analysis_result["face_count"] = len(face_locs)
                    analysis_result["face_locations"] = face_locs
                    analysis_result["face_encodings_count"] = len(face_encs_np)
                    log.info(f"Input image facial recognition: {len(face_locs)} faces located, {len(face_encs_np)} encoded.")

                    if profile_pics_info and face_encs_np:
                        analysis_result["profile_pic_matches"] = await self._compare_with_profile_pics(face_encs_np, profile_pics_info)
                    elif not face_encs_np: log.info("No faces in input image to compare.")
                    elif not profile_pics_info: log.info("No profile pictures to compare against.")
                except RuntimeError as fr_err: # From _perform_face_recognition
                    log.error("Facial recognition on input image failed", error=str(fr_err))
                    analysis_result["error_message"] = f"Facial recognition error: {str(fr_err)}"
                    analysis_result["status"] = "failed_analysis" # Partial failure
                except Exception as e_fr_gen:
                    log.error("Unexpected error during facial recognition part", exc_info=True)
                    analysis_result["error_message"] = f"Unexpected facial recognition error: {str(e_fr_gen)}"
                    analysis_result["status"] = "failed_analysis"
            
            if analysis_result["status"] == "pending": analysis_result["status"] = "analyzed"

        except FileNotFoundError as e: analysis_result.update({"status": "error", "error_message": str(e)})
        except ValueError as e: analysis_result.update({"status": "error", "error_message": str(e)})
        except Exception as e:
            log.error("Unexpected error during image analysis", exc_info=True)
            analysis_result.update({"status": "error", "error_message": f"Unexpected error: {str(e)}"})
        
        if analysis_result["status"] == "failed_analysis" and not analysis_result["error_message"]:
            analysis_result["error_message"] = "Facial recognition step encountered an issue."
        return analysis_result


# Configuration Models
class PlatformConfig(BaseModel):
    model_config = ConfigDict(validate_assignment=True)
    enabled: bool = False
    api_endpoint: Optional[str] = None
    api_type: Optional[str] = None
    rapidapi_host: Optional[str] = None
    rapidapi_key: Optional[SecretStr] = None
    default_region: Optional[str] = None
    default_count: Optional[int] = Field(None, ge=1)
    extra_config: Dict[str, Any] = Field(default_factory=dict)

    @field_validator('api_endpoint', 'rapidapi_host', 'rapidapi_key', mode='before')
    @classmethod
    def check_rapidapi_requirements(cls, v: Any, info: Any) -> Any:
        values = info.data
        if values.get('enabled') and values.get('api_type') == 'rapidapi':
            if info.field_name in ['api_endpoint', 'rapidapi_host', 'rapidapi_key']:
                if not v or (isinstance(v, (str, SecretStr)) and str(v if isinstance(v, str) else v.get_secret_value()) == "YOUR_RAPIDAPI_KEY_HERE"):
                    raise ValueError(f"'{info.field_name}' is required and not a placeholder for enabled RapidAPI.")
        if info.field_name == 'rapidapi_key' and v and not isinstance(v, SecretStr):
            return SecretStr(str(v))
        return v

class DatabaseConfig(BaseModel):
    model_config = ConfigDict(validate_assignment=True)
    dsn: SecretStr

class SearchConfig(BaseModel):
    model_config = ConfigDict(validate_assignment=True)
    default_name: str = "Example Name"
    photo_path: Optional[str] = None
    platforms_to_search: List[PlatformType] = Field(default_factory=lambda: [PlatformType.INSTAGRAM])

class SocialMapperConfig(BaseModel):
    model_config = ConfigDict(validate_assignment=True)
    security: SecurityConfig
    database: DatabaseConfig
    platforms: Dict[PlatformType, PlatformConfig]
    image_analysis: ImageAnalysisConfig = Field(default_factory=ImageAnalysisConfig)
    search: SearchConfig = Field(default_factory=SearchConfig)

    @field_validator('platforms')
    @classmethod
    def check_platform_config(cls, v: Dict[PlatformType, PlatformConfig]):
        if not any(p_config.enabled for p_config in v.values()):
            logger.warning("Configuration check: No platforms are enabled.")
        # Further validation per platform is handled by PlatformConfig model itself
        return v

# Core Application
class SocialMapperPro:
    def __init__(self, config: SocialMapperConfig):
        self.config = config
        self.security_config = config.security # Convenience alias
        
        self.database = AsyncDatabase(self.config.database.dsn.get_secret_value())
        self.network = NetworkManager(self.security_config)
        self.image_analyzer = ImageAnalysisEngine(self.security_config, self.config.image_analysis)
        
        self.integrators: Dict[PlatformType, PlatformIntegrator] = {}
        self._init_platforms()
        
        self._key_rotation_task: Optional[asyncio.Task] = None
        logger.info("SocialMapperPro components initialized.")

    def _init_platforms(self):
        integrator_classes: Dict[PlatformType, type[PlatformIntegrator]] = {
            PlatformType.FACEBOOK: FacebookIntegrator, PlatformType.TIKTOK: TikTokIntegrator,
            PlatformType.INSTAGRAM: InstagramIntegrator,
        }
        for platform_type, platform_conf_model in self.config.platforms.items():
            if platform_conf_model.enabled:
                if platform_type in integrator_classes:
                    # Pass config as dict. SecretStr will be exposed as str if model_dump() default used.
                    # If integrator expects SecretStr, pass platform_conf_model directly or handle SecretStr conversion.
                    # For now, assume model_dump() makes keys like rapidapi_key plain strings.
                    # Use model_dump(exclude_none=True) for cleaner config dicts.
                    config_dict = platform_conf_model.model_dump(exclude_none=True)
                    # Manually ensure SecretStr values are passed as strings for integrators expecting plain strings
                    if 'rapidapi_key' in config_dict and isinstance(platform_conf_model.rapidapi_key, SecretStr):
                         config_dict['rapidapi_key'] = platform_conf_model.rapidapi_key.get_secret_value()

                    self.integrators[platform_type] = integrator_classes[platform_type](config_dict, self.network)
                    logger.debug(f"Initialized integrator for enabled platform: {platform_type.value}")
                else:
                    logger.warning(f"Platform '{platform_type.value}' enabled but no integrator class found.")
            # else: logger.info(f"Platform '{platform_type.value}' is configured but disabled.")


    def _schedule_key_rotation(self):
        if self._key_rotation_task and not self._key_rotation_task.done():
            logger.warning("Key rotation task already scheduled.")
            return

        async def rotate_periodically():
            while True:
                try:
                    interval_sec = self.security_config.key_rotation_days * 86400
                    if interval_sec <= 0: # Should be ge=1 from Pydantic
                        logger.error("Invalid key rotation interval. Disabling rotation.")
                        break
                    
                    logger.info(f"Next security key rotation scheduled in {timedelta(seconds=interval_sec)}.")
                    await asyncio.sleep(interval_sec)
                    
                    logger.info("Performing scheduled security key rotation...")
                    self.security_config.rotate_key() # Updates key in SecurityConfig
                    
                    try: # Update Fernet instance in ImageAnalysisEngine
                        new_key_b64_bytes = self.security_config.encryption_key.get_secret_value().encode('ascii')
                        self.image_analyzer.fernet = Fernet(new_key_b64_bytes)
                        logger.info("ImageAnalysisEngine crypto re-initialized with new key.")
                    except Exception:
                        logger.critical("CRITICAL: Failed to update Fernet key in ImageAnalysisEngine post-rotation.", exc_info=True)
                    
                    await self.log_audit_event("SecurityKeyRotated", {"status": "success"})
                except asyncio.CancelledError:
                    logger.info("Key rotation task cancelled.")
                    break
                except Exception as e:
                    logger.error("Error in periodic key rotation task", exc_info=True)
                    await self.log_audit_event("SecurityKeyRotationFailed", {"error_message": str(e)})
                    await asyncio.sleep(3600) # Wait 1hr before retrying loop

        if self.security_config.key_rotation_days > 0:
            self._key_rotation_task = asyncio.create_task(rotate_periodically())
            logger.info("Periodic security key rotation task scheduled.")
        else:
            logger.warning("Security key rotation disabled (key_rotation_days <= 0).")

    async def initialize(self):
        try:
            await self.database.connect()
        except Exception:
            logger.critical("Failed to connect to database during initialization. Aborting.", exc_info=True)
            raise
        self._schedule_key_rotation()
        await self.log_audit_event("SystemInitialized", {"status": "success"})
        logger.info("SocialMapperPro initialized successfully.")
# In class SocialMapperPro:

    async def shutdown(self):
        logger.info("Shutting down SocialMapperPro...")
        if self._key_rotation_task and not self._key_rotation_task.done():
            self._key_rotation_task.cancel()
            try:
                await self._key_rotation_task
            except asyncio.CancelledError:
                logger.info("Key rotation task cancelled during shutdown.")
            except Exception: # Catch any other error from awaiting the cancelled task
                logger.error("Error awaiting cancelled key rotation task on shutdown", exc_info=True)
        
        # Wrap audit log call in shutdown to prevent it from masking original errors
        try:
            await self.log_audit_event("SystemShutdown", {"status": "pending"})
        except Exception as shutdown_audit_exc:
            logger.error(
                "Failed to log SystemShutdown audit event during shutdown sequence. This may mask an earlier error.",
                exc_info=True,  # Log traceback for this specific failure
                audit_error_message=str(shutdown_audit_exc)
            )

        # Close network resources
        if self.network: # Check if network manager exists
            await self.network.close()
        
        if self.image_analyzer and self.image_analyzer.network and self.image_analyzer.network is not self.network:
            await self.image_analyzer.network.close()
        
        if self.database: # Check if database object exists
            await self.database.disconnect()
            
        logger.info("SocialMapperPro shutdown completed.")

    async def log_audit_event(self, event_name: str, details: Dict[str, Any]):
        if self.config.audit_logging:
            sanitized_details = self._sanitize_audit_details(details)
            await self.database.log_audit(event_name, sanitized_details)
        # else: logger.debug("Audit logging disabled, skipping event", audit_event_name=event_name)


    def _sanitize_audit_details(self, details: Dict[str, Any]) -> Dict[str, Any]:
        sanitized = {}
        sensitive_keys_substrings = ['key', 'password', 'secret', 'token', 'credential', 'auth', 'dsn', 'encryption_key', 'api_key']
        
        for k, v in details.items():
            if isinstance(v, dict):
                sanitized[k] = self._sanitize_audit_details(v)
            elif isinstance(v, SecretStr):
                sanitized[k] = "***REDACTED (SecretStr)***"
            elif isinstance(v, Path):
                 sanitized[k] = str(v)
            elif any(s_key in k.lower() for s_key in sensitive_keys_substrings):
                 # Redact based on key name regardless of value type, if it's a common pattern for sensitive data
                 sanitized[k] = f"***REDACTED (key: {k})***"
            elif isinstance(v, (list, tuple)):
                sanitized_list = []
                for item in v:
                    if isinstance(item, dict): sanitized_list.append(self._sanitize_audit_details(item))
                    elif isinstance(item, SecretStr): sanitized_list.append("***REDACTED (SecretStr in list)***")
                    else: sanitized_list.append(item)
                sanitized[k] = sanitized_list
            else:
                sanitized[k] = v
        return sanitized

    async def _search_platform(self, platform_type: PlatformType, name: str) -> Dict[str, Any]:
        log = logger.bind(platform=platform_type.value, search_term=name)
        if platform_type not in self.integrators:
            log.warning("Integrator not available/enabled for this platform.")
            return {"status": "error", "error_message": f"Platform '{platform_type.value}' not configured or supported."}

        integrator = self.integrators[platform_type]
        log.info("Initiating platform search via integrator.")
        await self.log_audit_event("PlatformSearchStarted", {"platform": platform_type.value, "name": name})
        
        try:
            result_data = await integrator.search(name)
            final_result = {
                "status": "completed" if result_data.get("matches") is not None and not result_data.get("error") else "error",
                "matches": result_data.get("matches", []),
                "error_message": result_data.get("error")
            }
            await self.log_audit_event("PlatformSearchCompleted", {
                "platform": platform_type.value, "name": name, "status": final_result["status"],
                "match_count": len(final_result["matches"]), "error_info": final_result["error_message"]})
            log.info("Platform search finished.", status=final_result["status"], match_count=len(final_result["matches"]), error_present=bool(final_result["error_message"]))
            return final_result
        except Exception as exc:
            log.error("Platform search task failed unexpectedly within integrator.", exc_info=True)
            await self.log_audit_event("PlatformSearchFailed", {"platform": platform_type.value, "name": name, "error_message": str(exc)})
            return {"status": "error", "matches": [], "error_message": f"Unexpected error in {platform_type.value} search: {str(exc)}"}

    async def _analyze_image(self, photo_path: Optional[Path], platform_search_results: Dict[PlatformType, Dict[str, Any]]) -> Dict[str, Any]:
        log = logger.bind(photo_path=str(photo_path) if photo_path else "None")
        if photo_path is None:
            return {"status": "skipped", "reason": "No photo path provided"}
        if not self.config.image_analysis.enable_facial_recognition:
            return {"status": "skipped", "reason": "Facial recognition disabled in configuration."}

        log.info("Initiating image analysis.")
        await self.log_audit_event("ImageAnalysisStarted", {"photo_path": str(photo_path)})
        
        try:
            profile_pics_for_comparison: List[Dict[str, Any]] = []
            for platform_type_enum, result in platform_search_results.items():
                if result.get("status") == "completed":
                    for match in result.get("matches", []):
                        if match.get("profile_pic_url") and match.get("username"):
                            profile_pics_for_comparison.append({
                                "username": match["username"], "platform": platform_type_enum.value,
                                "profile_url": match.get("profile_url"),
                                "profile_pic_url": match["profile_pic_url"]})
            
            log.debug(f"Extracted {len(profile_pics_for_comparison)} profiles with pictures for face matching.")
            analysis_output = await self.image_analyzer.analyze(photo_path, profile_pics_for_comparison)
            
            await self.log_audit_event("ImageAnalysisCompleted", {
                "photo_path": str(photo_path), "status": analysis_output.get("status"),
                "face_count_input_image": analysis_output.get("face_count"),
                "profile_pic_match_count": len(analysis_output.get("profile_pic_matches", [])),
                "error_info": analysis_output.get("error_message")})
            log.info("Image analysis completed.", status=analysis_output.get('status'),
                       faces_found_input=analysis_output.get('face_count', 0),
                       profile_matches=len(analysis_output.get("profile_pic_matches", [])),
                       error_present=bool(analysis_output.get('error_message')))
            return analysis_output
        except Exception as exc:
            log.error("Image analysis task dispatch failed unexpectedly.", exc_info=True)
            await self.log_audit_event("ImageAnalysisFailed", {"photo_path": str(photo_path), "error_message": str(exc)})
            return {"status": "error", "error_message": f"Unexpected error launching image analysis: {str(exc)}"}

    def _generate_summary(self, full_results: Dict[str, Any]) -> Dict[str, Any]:
        platform_results = full_results.get("platform_results", {})
        image_analysis_result = full_results.get("image_analysis_result", {})
        total_profile_matches, errors_encountered = 0, []
        platform_statuses: Dict[str, str] = {}

        for platform_str, res_data in platform_results.items():
            status = res_data.get("status", "unknown")
            platform_statuses[platform_str] = status
            if status == "completed": total_profile_matches += len(res_data.get("matches", []))
            elif status == "error" and res_data.get("error_message"):
                errors_encountered.append(f"Platform {platform_str}: {res_data['error_message']}")
        
        img_status = image_analysis_result.get("status", "not_run")
        if img_status in ("error", "failed_analysis") and image_analysis_result.get("error_message"):
            errors_encountered.append(f"Image Analysis: {image_analysis_result['error_message']}")

        return {
            "overall_status": "completed_with_errors" if errors_encountered else "completed_successfully",
            "total_profiles_found": total_profile_matches,
            "platforms_queried_count": len(platform_results), "platform_statuses": platform_statuses,
            "image_analysis_status": img_status,
            "input_image_faces_detected": image_analysis_result.get("face_count", 0),
            "profile_picture_face_matches": len(image_analysis_result.get("profile_pic_matches", [])),
            "errors_summary": errors_encountered if errors_encountered else "No errors reported.",
        }

    async def full_analysis(self, name: str, photo_path: Optional[Path], platforms_to_search_enums: List[PlatformType]) -> Dict[str, Any]:
        start_time, query_id = datetime.now(), secrets.token_hex(8)
        log = logger.bind(query_id=query_id, search_name=name,
                          photo_path=str(photo_path) if photo_path else "N/A",
                          requested_platforms=[p.value for p in platforms_to_search_enums])
        log.info("Full analysis process started.")

        if not name or not name.strip():
            log.error("Analysis aborted: Search name cannot be empty.")
            return {"query_id": query_id, "error": "Search name is empty.", "status": "failed_precondition"}

        await self.log_audit_event("FullAnalysisStarted", {
            "query_id": query_id, "name": name, "photo_provided": photo_path is not None,
            "platforms_requested_count": len(platforms_to_search_enums)})

        results: Dict[str, Any] = {
            "query_id": query_id,
            "query_details": {"name_searched": name, "photo_path_analyzed": str(photo_path) if photo_path else None,
                              "platforms_queried": [p.value for p in platforms_to_search_enums],
                              "analysis_timestamp_utc": start_time.isoformat()},
            "platform_results": {}, "image_analysis_result": {}, "summary": {}, "analysis_duration_seconds": 0.0
        }

        active_platforms = [ptype for ptype in platforms_to_search_enums if ptype in self.integrators]
        for p_skipped in set(platforms_to_search_enums) - set(active_platforms):
            results["platform_results"][p_skipped.value] = {"status": "skipped", "reason": "Platform not enabled/integrator missing."}
            log.warning(f"Skipping platform {p_skipped.value} as it's not active/configured.")

        platform_tasks = [(ptype, asyncio.create_task(self._search_platform(ptype, name), name=f"search-{ptype.value}-{query_id}"))
                          for ptype in active_platforms]
        for ptype_enum, _ in platform_tasks: results["platform_results"][ptype_enum.value] = {"status": "pending_search"}
        
        outcomes = await asyncio.gather(*[task for _, task in platform_tasks], return_exceptions=True)
        for i, (ptype_enum, _) in enumerate(platform_tasks):
            outcome, p_key_str = outcomes[i], ptype_enum.value
            if isinstance(outcome, Exception):
                log.error(f"Platform search task for {p_key_str} failed.", exc_info=outcome)
                results["platform_results"][p_key_str] = {"status": "error", "matches": [], "error_message": f"Task failed: {str(outcome)}"}
            else: results["platform_results"][p_key_str] = outcome
        
        # Map string keys from results back to PlatformType for _analyze_image
        platform_results_for_img_analysis: Dict[PlatformType, Dict[str, Any]] = {
            PlatformType(p_str): data for p_str, data in results["platform_results"].items() if p_str in PlatformType._value2member_map_
        }
        img_task = asyncio.create_task(self._analyze_image(photo_path, platform_results_for_img_analysis), name=f"image-analysis-{query_id}")
        try: results["image_analysis_result"] = await img_task
        except Exception as img_task_exc:
            log.error("Image analysis task failed at gather/await stage.", exc_info=img_task_exc)
            results["image_analysis_result"] = {"status": "error", "error_message": f"Task execution error: {str(img_task_exc)}"}

        results["analysis_duration_seconds"] = round((datetime.now() - start_time).total_seconds(), 3)
        results["summary"] = self._generate_summary(results)
        log.info("Full analysis process completed.", duration_sec=results["analysis_duration_seconds"], overall_status=results["summary"]["overall_status"])
        await self.log_audit_event("FullAnalysisCompleted", {"query_id": query_id, "duration_seconds": results["analysis_duration_seconds"], "summary_snapshot": results["summary"]})
        return results


# --- Main Execution & Configuration Loading ---
async def _load_or_generate_config(config_path: Path) -> Optional[SocialMapperConfig]:
    if not config_path.exists():
        logger.warning(f"Configuration file '{config_path}' not found. Generating a default.")
        try:
            default_fernet_key = Fernet.generate_key().decode('ascii')
            default_config_data = {
                "security": {"encryption_key": default_fernet_key, "key_rotation_days": 7, "sanitize_inputs": True,
                             "request_signatures": True, "audit_logging": True, "max_retries": 3, "max_file_size": 10_485_760,
                             "network_timeout_seconds": 30.0, "network_max_connections": 100, "network_rate_limit_permits": 50,
                             "circuit_breaker_failure_threshold": 10, "circuit_breaker_open_delay_seconds": 60,
                             "circuit_breaker_half_open_delay_seconds": 120},
                "database": {"dsn": "postgresql+asyncpg://user:pass@localhost:5432/socialsearchdb_dev"},
                "platforms": {
                    PlatformType.FACEBOOK.value: {"enabled": False}, PlatformType.TIKTOK.value: {"enabled": False},
                    PlatformType.INSTAGRAM.value: {"enabled": False, "api_type": "rapidapi", # Disabled by default
                                                   "api_endpoint": "https://rocketapi-for-developers.p.rapidapi.com/instagram/search",
                                                   "rapidapi_host": "rocketapi-for-developers.p.rapidapi.com",
                                                   "rapidapi_key": "YOUR_RAPIDAPI_KEY_HERE", "default_count": 10, "extra_config": {}}},
                "image_analysis": {"enable_facial_recognition": True, "face_detection_model": "hog", "min_confidence_threshold": 0.6},
                "search": {"default_name": "John Doe", "photo_path": None, "platforms_to_search": [PlatformType.INSTAGRAM.value]}
            }
            async with aiofiles.open(config_path, "w") as f: await f.write(json.dumps(default_config_data, indent=4))
            logger.info(f"Default configuration saved to '{config_path}'. Review and update API keys/DSN, then re-run.")
            return None
        except Exception:
            logger.error(f"Fatal: Could not generate/write default config at '{config_path}'.", exc_info=True)
            return None

    try:
        logger.info(f"Loading configuration from '{config_path}'...")
        async with aiofiles.open(config_path, "r") as f: config_data = json.loads(await f.read())
        
        if "platforms" in config_data: # Convert string keys to PlatformType enums
            config_data["platforms"] = {PlatformType(k) if isinstance(k, str) else k: v for k, v in config_data["platforms"].items()}
        if "search" in config_data and "platforms_to_search" in config_data["search"]:
             config_data["search"]["platforms_to_search"] = [PlatformType(p) if isinstance(p, str) else p for p in config_data["search"]["platforms_to_search"]]

        validated_config = SocialMapperConfig(**config_data)
        logger.info("Configuration loaded and validated successfully.")
        
        for p_type, p_conf in validated_config.platforms.items():
            if p_conf.enabled and p_conf.api_type == 'rapidapi' and \
               (not p_conf.rapidapi_key or p_conf.rapidapi_key.get_secret_value() == "YOUR_RAPIDAPI_KEY_HERE"):
                logger.error(f"Platform '{p_type.value}' enabled with RapidAPI, but 'rapidapi_key' is placeholder in '{config_path}'.")
                return None
        return validated_config
    except (FileNotFoundError, json.JSONDecodeError) as e:
        logger.error(f"Critical: Error loading '{config_path}': {e}", exc_info=True)
    except Exception: # Catches Pydantic ValidationError, etc.
        logger.error(f"Critical: Configuration loading or validation failed from '{config_path}'.", exc_info=True)
    return None

def _json_serializer(obj: Any) -> Any:
    if isinstance(obj, (Path, SecretStr, Enum)): return str(obj) # Path, SecretStr to string, Enum to value
    if isinstance(obj, np.ndarray): return obj.tolist()
    if isinstance(obj, (datetime, timedelta)): return obj.isoformat()
    raise TypeError(f"Object of type {type(obj).__name__} is not JSON serializable")

def _save_results_to_file(results: Dict[str, Any], query_id: str, output_dir: Path = Path(".")):
    output_dir.mkdir(parents=True, exist_ok=True)
    results_file_path = output_dir / f"social_mapper_results_{query_id}.json"
    try:
        with open(results_file_path, "w") as f:
            json.dump(results, f, indent=2, default=_json_serializer)
        logger.info(f"Analysis results saved to: {results_file_path.resolve()}")
        print(f"\nINFO: Results saved to {results_file_path.resolve()}")
    except Exception:
        logger.error(f"Failed to save results to file '{results_file_path}'.", exc_info=True)
        print(f"\nERROR: Could not save results to {results_file_path}.")

def _print_formatted_results(results: Dict[str, Any]):
    print("\n" + "="*25 + " Social Media Analysis Results Summary " + "="*25)
    # For console, we might want more aggressive summarization.
    # Using json.dumps with the custom serializer for consistent output.
    printable_summary = results.get("summary", {})
    printable_query = results.get("query_details", {})
    
    print("\n--- Overall Summary ---")
    print(json.dumps(printable_summary, indent=2, default=_json_serializer))
    print("\n--- Query Details ---")
    print(json.dumps(printable_query, indent=2, default=_json_serializer))
    
    if "platform_results" in results:
        print("\n--- Platform Search Results (Summary) ---")
        for platform, presult in results["platform_results"].items():
            status = presult.get('status', 'unknown')
            match_count = len(presult.get('matches', []))
            error_msg = presult.get('error_message', '')
            print(f"\n  Platform: {platform} (Status: {status}, Matches: {match_count}{', Error: ' + error_msg if error_msg else ''})")
            if presult.get('matches'):
                for match in presult['matches'][:2]: # Print first 2 matches summary
                    print(f"      - User: {match.get('username', 'N/A')}, Name: {match.get('name', 'N/A')}, URL: {match.get('profile_url', 'N/A')}")
                if match_count > 2: print(f"      ... and {match_count - 2} more matches.")
    
    if "image_analysis_result" in results and results["image_analysis_result"].get("status") not in ("skipped", "pending"):
        print("\n--- Image Analysis (Summary) ---")
        img_res = results["image_analysis_result"]
        print(f"  Status: {img_res.get('status')}")
        if img_res.get('error_message'): print(f"    Error: {img_res.get('error_message')}")
        print(f"    Input Image Faces Detected: {img_res.get('face_count', 0)}")
        match_count = len(img_res.get('profile_pic_matches', []))
        if match_count > 0:
            print(f"    Profile Picture Face Matches ({match_count}):")
            for match in img_res['profile_pic_matches'][:2]: # Print first 2
                print(f"      - User: {match.get('username')}, Platform: {match.get('platform')}, Confidence (dist): {match.get('match_confidence_distance', 'N/A'):.4f}")
    print("\n" + "="*80 + "\n")


async def main_async_runner():
    script_dir = Path(__file__).parent.resolve()
    config_file_path = script_dir / "config.json"
    results_output_dir = script_dir / "analysis_results"

    app_config = await _load_or_generate_config(config_file_path)
    if not app_config:
        logger.error("Application cannot start due to configuration issues.")
        print("ERROR: Application startup failed. Check logs and config.json.")
        return

    mapper_instance = None
    try:
        mapper_instance = SocialMapperPro(app_config)
        await mapper_instance.initialize()

        search_settings = app_config.search
        name_to_search = search_settings.default_name.strip()
        
        resolved_photo_path: Optional[Path] = None
        if search_settings.photo_path:
            candidate_path = Path(search_settings.photo_path)
            if not candidate_path.is_absolute(): candidate_path = script_dir / candidate_path
            if await asyncio.to_thread(candidate_path.is_file):
                resolved_photo_path = candidate_path.resolve()
                logger.info(f"Using photo for analysis: {resolved_photo_path}")
            else:
                logger.warning(f"Photo path '{search_settings.photo_path}' (resolved: '{candidate_path}') not found. Image analysis limited.")
        
        platforms_enums = [p_val for p_val in search_settings.platforms_to_search if isinstance(p_val, PlatformType)]
        
        if not name_to_search or not platforms_enums:
            msg = "Search name or platforms list is empty in config. Aborting."
            logger.error(msg)
            print(f"ERROR: {msg}")
            return

        logger.info(f"Starting analysis: name='{name_to_search}'", photo=str(resolved_photo_path), platforms=[p.value for p in platforms_enums])
        analysis_results = await mapper_instance.full_analysis(name_to_search, resolved_photo_path, platforms_enums)

        if analysis_results and "query_id" in analysis_results:
            _save_results_to_file(analysis_results, analysis_results["query_id"], results_output_dir)
            _print_formatted_results(analysis_results)
        else: logger.error("Analysis did not produce valid results.")

    except Exception: # Catch-all for unexpected issues
        logger.critical("A critical unhandled error occurred during main execution.", exc_info=True)
        print(f"CRITICAL UNHANDLED ERROR - Check logs.")
    finally:
        if mapper_instance: await mapper_instance.shutdown()
        logger.info("Application main execution finished.")
        print("\nApplication run complete. Check logs for details.")

if __name__ == "__main__":
    try:
        asyncio.run(main_async_runner())
    except KeyboardInterrupt:
        logger.info("Application terminated by user (KeyboardInterrupt).")
        print("\nApplication terminated by user.")
    except Exception: # Fallback for errors not caught within main_async_runner
        logger.critical("Fatal error during asyncio.run or unhandled in main_async_runner.", exc_info=True)
        print(f"FATAL ERROR - Check logs.")
